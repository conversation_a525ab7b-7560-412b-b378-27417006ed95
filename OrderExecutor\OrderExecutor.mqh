#include "../mql4-lib/Trade/OrderManager.mqh"
#include "../mql4-lib/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 訂單執行請求類別                                                 |
//+------------------------------------------------------------------+

class OrderRequest{
private:
    int         m_op;
    double      m_lots;
    int         m_stoploss;
    int         m_takeprofit;
    string      m_comment;
public:
    OrderRequest(int op,double lots,int stoploss,int takeprofit,string comment)
    : m_op(op), m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}
    int         GetOp() const { return m_op; }
    double      GetLots() const { return m_lots; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
    string      GetComment() const { return m_comment; }
};

class BatchOrderRequest{
private:
    Vector<OrderRequest*> m_requests;
public:
    void            AddRequest(OrderRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    OrderRequest*   GetRequest(int index) const { return m_requests[index]; }
};

class ModifyRequest{
private:
    int         m_ticket;
    int         m_stoploss;
    int         m_takeprofit;
public:
    ModifyRequest(int ticket,int stoploss,int takeprofit)
    : m_ticket(ticket), m_stoploss(stoploss), m_takeprofit(takeprofit) {}
    int         GetTicket() const { return m_ticket; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
};

class BatchModifyRequest{
private:
    Vector<ModifyRequest*> m_requests;
public:
    void            AddRequest(ModifyRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    ModifyRequest*  GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| 關閉訂單請求類別                                                 |
//+------------------------------------------------------------------+
class CloseRequest{
private:
    int         m_ticket;
    double      m_lots;        // 0 表示完全關閉
public:
    CloseRequest(int ticket, double lots = 0.0)
    : m_ticket(ticket), m_lots(lots) {}
    int         GetTicket() const { return m_ticket; }
    double      GetLots() const { return m_lots; }
    bool        IsPartialClose() const { return m_lots > 0.0; }
};

//+------------------------------------------------------------------+
//| 批次關閉訂單請求類別                                             |
//+------------------------------------------------------------------+
class BatchCloseRequest{
private:
    Vector<CloseRequest*> m_requests;
public:
    void            AddRequest(CloseRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    CloseRequest*   GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| OrderExecutor 類別 - 訂單執行器                                  |
//| 提供單一和批次訂單操作功能                                       |
//+------------------------------------------------------------------+
class OrderExecutor : public OrderManager
{
private:
    //--- 驗證請求細節
    static bool     ValidateBatchOrderRequest(BatchOrderRequest* request);
    static bool     ValidateBatchModifyRequest(BatchModifyRequest* request);
    static bool     ValidateBatchCloseRequest(BatchCloseRequest* request);

private:
    //--- 驗證請求細節
    static bool     ValidateOrderRequest(OrderRequest* request, string symbol);
    static bool     ValidateModifyRequest(ModifyRequest* request, string symbol);
    static bool     ValidateCloseRequest(CloseRequest* request, string symbol);

public:
    //--- 構造函數
    OrderExecutor(string symbol) : OrderManager(symbol) {}

    //--- 單一訂單操作方法
    int             Market(OrderRequest* request);
    bool            Modify(ModifyRequest* request);
    bool            Close(CloseRequest* request);

    //--- 批次訂單操作方法
    Vector<int>*    BatchMarket(BatchOrderRequest* request);
    Vector<bool>*   BatchModify(BatchModifyRequest* request);
    Vector<bool>*   BatchClose(BatchCloseRequest* request);
};

//+------------------------------------------------------------------+
//| OrderExecutor 方法實現                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 執行市價單請求                                                   |
//+------------------------------------------------------------------+
int OrderExecutor::Market(OrderRequest* request)
{
    // 使用詳細驗證方法
    if(!ValidateOrderRequest(request, s))
    {
        return -1;
    }

    return market(request.GetOp(),
                  request.GetLots(),
                  request.GetStopLoss(),
                  request.GetTakeProfit(),
                  request.GetComment());
}



//+------------------------------------------------------------------+
//| 修改訂單請求                                                     |
//+------------------------------------------------------------------+
bool OrderExecutor::Modify(ModifyRequest* request)
{
    // 使用詳細驗證方法
    if(!ValidateModifyRequest(request, s))
    {
        return false;
    }

    return modify(request.GetTicket(),
                  request.GetStopLoss(),
                  request.GetTakeProfit());
}

//+------------------------------------------------------------------+
//| 關閉訂單請求                                                     |
//+------------------------------------------------------------------+
bool OrderExecutor::Close(CloseRequest* request)
{
    // 使用詳細驗證方法
    if(!ValidateCloseRequest(request, s))
    {
        return false;
    }

    int ticket = request.GetTicket();

    if(request.IsPartialClose())
    {
        double lots = request.GetLots();
        Print("資訊 [OrderExecutor::Close]: 執行部分關閉，票號 ", ticket, "，手數 ", lots);
        return close(ticket, lots);
    }
    else
    {
        Print("資訊 [OrderExecutor::Close]: 執行完全關閉，票號 ", ticket);
        return close(ticket);
    }
}



//+------------------------------------------------------------------+
//| 批次市價單執行                                                   |
//+------------------------------------------------------------------+
Vector<int>* OrderExecutor::BatchMarket(BatchOrderRequest* request)
{
    // 使用靜態驗證方法進行預驗證
    if(!ValidateBatchOrderRequest(request))
    {
        return NULL;
    }

    int count = request.GetRequestCount();
    Print("資訊 [OrderExecutor::BatchMarket]: 開始處理批次市價單，共 ", count, " 個訂單");

    Vector<int>* results = new Vector<int>(false); // 不擁有元素
    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < count; i++)
    {
        OrderRequest* orderReq = request.GetRequest(i);
        if(orderReq == NULL)
        {
            Print("錯誤 [OrderExecutor::BatchMarket]: 第 ", i+1, " 個 OrderRequest 為空，跳過");
            results.add(-1);
            failureCount++;
            continue;
        }

        int ticket = Market(orderReq);
        results.add(ticket);

        if(ticket > 0)
        {
            successCount++;
            Print("成功 [OrderExecutor::BatchMarket]: 第 ", i+1, " 個訂單執行成功，票號 ", ticket);
        }
        else
        {
            failureCount++;
            Print("失敗 [OrderExecutor::BatchMarket]: 第 ", i+1, " 個訂單執行失敗");
        }
    }

    Print("資訊 [OrderExecutor::BatchMarket]: 批次處理完成，成功 ", successCount, " 個，失敗 ", failureCount, " 個");
    return results;
}

//+------------------------------------------------------------------+
//| 批次訂單修改                                                     |
//+------------------------------------------------------------------+
Vector<bool>* OrderExecutor::BatchModify(BatchModifyRequest* request)
{
    // 使用靜態驗證方法進行預驗證
    if(!ValidateBatchModifyRequest(request))
    {
        return NULL;
    }

    int count = request.GetRequestCount();
    Print("資訊 [OrderExecutor::BatchModify]: 開始處理批次修改，共 ", count, " 個訂單");

    Vector<bool>* results = new Vector<bool>(false); // 不擁有元素
    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < count; i++)
    {
        ModifyRequest* modifyReq = request.GetRequest(i);
        if(modifyReq == NULL)
        {
            Print("錯誤 [OrderExecutor::BatchModify]: 第 ", i+1, " 個 ModifyRequest 為空，跳過");
            results.add(false);
            failureCount++;
            continue;
        }

        bool success = Modify(modifyReq);
        results.add(success);

        if(success)
        {
            successCount++;
            Print("成功 [OrderExecutor::BatchModify]: 第 ", i+1, " 個訂單修改成功，票號 ", modifyReq.GetTicket());
        }
        else
        {
            failureCount++;
            Print("失敗 [OrderExecutor::BatchModify]: 第 ", i+1, " 個訂單修改失敗，票號 ", modifyReq.GetTicket());
        }
    }

    Print("資訊 [OrderExecutor::BatchModify]: 批次處理完成，成功 ", successCount, " 個，失敗 ", failureCount, " 個");
    return results;
}

//+------------------------------------------------------------------+
//| 批次訂單關閉                                                     |
//+------------------------------------------------------------------+
Vector<bool>* OrderExecutor::BatchClose(BatchCloseRequest* request)
{
    // 使用靜態驗證方法進行預驗證
    if(!ValidateBatchCloseRequest(request))
    {
        return NULL;
    }

    int count = request.GetRequestCount();
    Print("資訊 [OrderExecutor::BatchClose]: 開始處理批次關閉，共 ", count, " 個訂單");

    Vector<bool>* results = new Vector<bool>(false); // 不擁有元素
    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < count; i++)
    {
        CloseRequest* closeReq = request.GetRequest(i);
        if(closeReq == NULL)
        {
            Print("錯誤 [OrderExecutor::BatchClose]: 第 ", i+1, " 個 CloseRequest 為空，跳過");
            results.add(false);
            failureCount++;
            continue;
        }

        bool success = Close(closeReq);
        results.add(success);

        if(success)
        {
            successCount++;
            string closeType = closeReq.IsPartialClose() ? "部分關閉" : "完全關閉";
            Print("成功 [OrderExecutor::BatchClose]: 第 ", i+1, " 個訂單", closeType, "成功，票號 ", closeReq.GetTicket());
        }
        else
        {
            failureCount++;
            Print("失敗 [OrderExecutor::BatchClose]: 第 ", i+1, " 個訂單關閉失敗，票號 ", closeReq.GetTicket());
        }
    }

    Print("資訊 [OrderExecutor::BatchClose]: 批次處理完成，成功 ", successCount, " 個，失敗 ", failureCount, " 個");
    return results;
}

//+------------------------------------------------------------------+
//| 私有驗證方法實現                                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 驗證 OrderRequest 請求                                           |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateOrderRequest(OrderRequest* request, string symbol)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: OrderRequest 指針為空");
        return false;
    }

    // 驗證操作類型
    int op = request.GetOp();
    if(op != OP_BUY && op != OP_SELL)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 無效的操作類型 ", op, "，只支援 OP_BUY(0) 和 OP_SELL(1)");
        return false;
    }

    // 驗證手數
    double lots = request.GetLots();
    if(lots <= 0.0)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 無效的手數 ", lots, "，必須大於 0");
        return false;
    }

    // 檢查手數是否符合經紀商要求
    double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);

    if(lots < minLot)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 手數 ", lots, " 小於最小允許值 ", minLot);
        return false;
    }

    if(lots > maxLot)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 手數 ", lots, " 大於最大允許值 ", maxLot);
        return false;
    }

    // 檢查手數步長
    double remainder = MathMod(lots - minLot, lotStep);
    if(remainder > 0.000001) // 使用小的容差值
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 手數 ", lots, " 不符合步長要求 ", lotStep);
        return false;
    }

    // 驗證止損和止盈
    int stoploss = request.GetStopLoss();
    int takeprofit = request.GetTakeProfit();

    if(stoploss < 0)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 無效的止損點數 ", stoploss, "，不能為負值");
        return false;
    }

    if(takeprofit < 0)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 無效的止盈點數 ", takeprofit, "，不能為負值");
        return false;
    }

    // 檢查止損止盈距離是否符合最小距離要求
    int stopLevel = (int)SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL);

    if(stoploss > 0 && stoploss < stopLevel)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 止損距離 ", stoploss, " 點小於最小要求 ", stopLevel, " 點");
        return false;
    }

    if(takeprofit > 0 && takeprofit < stopLevel)
    {
        Print("錯誤 [OrderExecutor::ValidateOrderRequest]: 止盈距離 ", takeprofit, " 點小於最小要求 ", stopLevel, " 點");
        return false;
    }

    // 驗證註釋長度
    string comment = request.GetComment();
    if(StringLen(comment) > 31) // MQL4 註釋最大長度限制
    {
        Print("警告 [OrderExecutor::ValidateOrderRequest]: 註釋長度 ", StringLen(comment), " 超過31字符，將被截斷");
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證 ModifyRequest 請求                                          |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateModifyRequest(ModifyRequest* request, string symbol)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: ModifyRequest 指針為空");
        return false;
    }

    // 驗證票號
    int ticket = request.GetTicket();
    if(ticket <= 0)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 無效的訂單票號 ", ticket, "，必須大於 0");
        return false;
    }

    // 檢查訂單是否存在
    if(!OrderSelect(ticket, SELECT_BY_TICKET))
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 找不到票號為 ", ticket, " 的訂單");
        return false;
    }

    // 檢查訂單是否屬於當前交易品種
    if(OrderSymbol() != symbol)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 訂單 ", ticket, " 不屬於當前交易品種 ", symbol, "，實際品種為 ", OrderSymbol());
        return false;
    }

    // 檢查訂單是否已關閉
    if(OrderCloseTime() > 0)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 訂單 ", ticket, " 已關閉，無法修改");
        return false;
    }

    // 驗證止損和止盈
    int stoploss = request.GetStopLoss();
    int takeprofit = request.GetTakeProfit();

    if(stoploss < 0)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 無效的止損點數 ", stoploss, "，不能為負值");
        return false;
    }

    if(takeprofit < 0)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 無效的止盈點數 ", takeprofit, "，不能為負值");
        return false;
    }

    // 檢查是否至少有一個參數需要修改
    if(stoploss == 0 && takeprofit == 0)
    {
        Print("警告 [OrderExecutor::ValidateModifyRequest]: 止損和止盈都為 0，沒有需要修改的內容");
        return true; // 不是錯誤，只是警告
    }

    // 檢查止損止盈距離是否符合最小距離要求
    int stopLevel = (int)SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL);

    if(stoploss > 0 && stoploss < stopLevel)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 止損距離 ", stoploss, " 點小於最小要求 ", stopLevel, " 點");
        return false;
    }

    if(takeprofit > 0 && takeprofit < stopLevel)
    {
        Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 止盈距離 ", takeprofit, " 點小於最小要求 ", stopLevel, " 點");
        return false;
    }

    // 對於掛單，檢查止損止盈是否與開倉價格方向正確
    int orderType = OrderType();
    double openPrice = OrderOpenPrice();

    if(orderType > 1) // 掛單
    {
        if(stoploss > 0)
        {
            double slPrice = 0;
            if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP)
            {
                slPrice = openPrice - stoploss * Point;
                if(slPrice >= openPrice)
                {
                    Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 買入掛單的止損價格 ", slPrice, " 必須小於開倉價格 ", openPrice);
                    return false;
                }
            }
            else if(orderType == OP_SELLLIMIT || orderType == OP_SELLSTOP)
            {
                slPrice = openPrice + stoploss * Point;
                if(slPrice <= openPrice)
                {
                    Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 賣出掛單的止損價格 ", slPrice, " 必須大於開倉價格 ", openPrice);
                    return false;
                }
            }
        }

        if(takeprofit > 0)
        {
            double tpPrice = 0;
            if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP)
            {
                tpPrice = openPrice + takeprofit * Point;
                if(tpPrice <= openPrice)
                {
                    Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 買入掛單的止盈價格 ", tpPrice, " 必須大於開倉價格 ", openPrice);
                    return false;
                }
            }
            else if(orderType == OP_SELLLIMIT || orderType == OP_SELLSTOP)
            {
                tpPrice = openPrice - takeprofit * Point;
                if(tpPrice >= openPrice)
                {
                    Print("錯誤 [OrderExecutor::ValidateModifyRequest]: 賣出掛單的止盈價格 ", tpPrice, " 必須小於開倉價格 ", openPrice);
                    return false;
                }
            }
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證 CloseRequest 請求                                           |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateCloseRequest(CloseRequest* request, string symbol)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: CloseRequest 指針為空");
        return false;
    }

    // 驗證票號
    int ticket = request.GetTicket();
    if(ticket <= 0)
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 無效的訂單票號 ", ticket, "，必須大於 0");
        return false;
    }

    // 檢查訂單是否存在
    if(!OrderSelect(ticket, SELECT_BY_TICKET))
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 找不到票號為 ", ticket, " 的訂單");
        return false;
    }

    // 檢查訂單是否屬於當前交易品種
    if(OrderSymbol() != symbol)
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 訂單 ", ticket, " 不屬於當前交易品種 ", symbol, "，實際品種為 ", OrderSymbol());
        return false;
    }

    // 檢查訂單是否已關閉
    if(OrderCloseTime() > 0)
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 訂單 ", ticket, " 已關閉，無法再次關閉");
        return false;
    }

    // 檢查是否為市價單（只有市價單可以關閉）
    int orderType = OrderType();
    if(orderType != OP_BUY && orderType != OP_SELL)
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 訂單 ", ticket, " 不是市價單（類型: ", orderType, "），無法關閉。請使用刪除掛單功能");
        return false;
    }

    // 驗證手數（如果是部分關閉）
    double lots = request.GetLots();
    if(request.IsPartialClose())
    {
        if(lots <= 0.0)
        {
            Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 部分關閉的手數 ", lots, " 無效，必須大於 0");
            return false;
        }

        // 檢查關閉手數是否超過訂單手數
        double orderLots = OrderLots();
        if(lots > orderLots)
        {
            Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 關閉手數 ", lots, " 超過訂單手數 ", orderLots);
            return false;
        }

        // 檢查手數步長
        double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
        double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);

        if(lots < minLot)
        {
            Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 關閉手數 ", lots, " 小於最小允許值 ", minLot);
            return false;
        }

        double remainder = MathMod(lots - minLot, lotStep);
        if(remainder > 0.000001) // 使用小的容差值
        {
            Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 關閉手數 ", lots, " 不符合步長要求 ", lotStep);
            return false;
        }

        // 檢查剩餘手數是否符合最小手數要求
        double remainingLots = orderLots - lots;
        if(remainingLots > 0 && remainingLots < minLot)
        {
            Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 部分關閉後剩餘手數 ", remainingLots, " 小於最小允許值 ", minLot, "，請完全關閉或調整關閉手數");
            return false;
        }
    }

    // 檢查市場是否開放交易
    if(!SymbolInfoInteger(symbol, SYMBOL_TRADE_MODE))
    {
        Print("錯誤 [OrderExecutor::ValidateCloseRequest]: 交易品種 ", symbol, " 當前不允許交易");
        return false;
    }

    // 檢查是否在交易時間內
    datetime currentTime = TimeCurrent();
    datetime sessionStart = (datetime)SymbolInfoInteger(symbol, SYMBOL_SESSION_DEALS_FROM);
    datetime sessionEnd = (datetime)SymbolInfoInteger(symbol, SYMBOL_SESSION_DEALS_TO);

    if(sessionStart > 0 && sessionEnd > 0)
    {
        int currentHour = TimeHour(currentTime);
        int startHour = TimeHour(sessionStart);
        int endHour = TimeHour(sessionEnd);

        if(startHour <= endHour) // 同一天內的交易時間
        {
            if(currentHour < startHour || currentHour >= endHour)
            {
                Print("警告 [OrderExecutor::ValidateCloseRequest]: 當前時間 ", TimeToStr(currentTime), " 可能不在交易時間內");
            }
        }
        else // 跨天的交易時間
        {
            if(currentHour < startHour && currentHour >= endHour)
            {
                Print("警告 [OrderExecutor::ValidateCloseRequest]: 當前時間 ", TimeToStr(currentTime), " 可能不在交易時間內");
            }
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| 靜態批次驗證方法實現                                             |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 驗證 BatchOrderRequest 請求                                      |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateBatchOrderRequest(BatchOrderRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: BatchOrderRequest 指針為空");
        return false;
    }

    int count = request.GetRequestCount();
    if(count <= 0)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 批次請求為空，沒有訂單需要處理");
        return false;
    }

    // 檢查批次大小限制
    const int MAX_BATCH_SIZE = 100; // 設定合理的批次大小限制
    if(count > MAX_BATCH_SIZE)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 批次大小 ", count, " 超過最大限制 ", MAX_BATCH_SIZE);
        return false;
    }

    Print("資訊 [OrderExecutor::ValidateBatchOrderRequest]: 開始驗證批次訂單請求，共 ", count, " 個請求");

    int validCount = 0;
    int invalidCount = 0;

    // 逐一驗證每個請求
    for(int i = 0; i < count; i++)
    {
        OrderRequest* orderReq = request.GetRequest(i);

        if(orderReq == NULL)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 第 ", i+1, " 個 OrderRequest 為空");
            invalidCount++;
            continue;
        }

        // 基本參數驗證
        int op = orderReq.GetOp();
        if(op != OP_BUY && op != OP_SELL)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 第 ", i+1, " 個請求操作類型無效: ", op);
            invalidCount++;
            continue;
        }

        double lots = orderReq.GetLots();
        if(lots <= 0.0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 第 ", i+1, " 個請求手數無效: ", lots);
            invalidCount++;
            continue;
        }

        int stoploss = orderReq.GetStopLoss();
        int takeprofit = orderReq.GetTakeProfit();

        if(stoploss < 0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 第 ", i+1, " 個請求止損無效: ", stoploss);
            invalidCount++;
            continue;
        }

        if(takeprofit < 0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 第 ", i+1, " 個請求止盈無效: ", takeprofit);
            invalidCount++;
            continue;
        }

        // 檢查註釋長度
        string comment = orderReq.GetComment();
        if(StringLen(comment) > 31)
        {
            Print("警告 [OrderExecutor::ValidateBatchOrderRequest]: 第 ", i+1, " 個請求註釋過長，將被截斷");
        }

        validCount++;
    }

    Print("資訊 [OrderExecutor::ValidateBatchOrderRequest]: 驗證完成，有效 ", validCount, " 個，無效 ", invalidCount, " 個");

    // 如果所有請求都無效，返回失敗
    if(validCount == 0)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchOrderRequest]: 所有請求都無效，批次操作取消");
        return false;
    }

    // 如果有部分無效請求，發出警告但允許繼續
    if(invalidCount > 0)
    {
        Print("警告 [OrderExecutor::ValidateBatchOrderRequest]: 有 ", invalidCount, " 個無效請求將被跳過");
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證 BatchModifyRequest 請求                                     |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateBatchModifyRequest(BatchModifyRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: BatchModifyRequest 指針為空");
        return false;
    }

    int count = request.GetRequestCount();
    if(count <= 0)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 批次請求為空，沒有修改需要處理");
        return false;
    }

    // 檢查批次大小限制
    const int MAX_BATCH_SIZE = 100;
    if(count > MAX_BATCH_SIZE)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 批次大小 ", count, " 超過最大限制 ", MAX_BATCH_SIZE);
        return false;
    }

    Print("資訊 [OrderExecutor::ValidateBatchModifyRequest]: 開始驗證批次修改請求，共 ", count, " 個請求");

    int validCount = 0;
    int invalidCount = 0;
    int duplicateCount = 0;

    // 用於檢查重複票號的陣列
    int tickets[];
    ArrayResize(tickets, count);

    // 逐一驗證每個請求
    for(int i = 0; i < count; i++)
    {
        ModifyRequest* modifyReq = request.GetRequest(i);

        if(modifyReq == NULL)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 第 ", i+1, " 個 ModifyRequest 為空");
            invalidCount++;
            continue;
        }

        // 基本參數驗證
        int ticket = modifyReq.GetTicket();
        if(ticket <= 0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 第 ", i+1, " 個請求票號無效: ", ticket);
            invalidCount++;
            continue;
        }

        // 檢查重複票號
        bool isDuplicate = false;
        for(int j = 0; j < validCount; j++)
        {
            if(tickets[j] == ticket)
            {
                Print("警告 [OrderExecutor::ValidateBatchModifyRequest]: 第 ", i+1, " 個請求票號 ", ticket, " 重複");
                isDuplicate = true;
                duplicateCount++;
                break;
            }
        }

        if(isDuplicate)
        {
            invalidCount++;
            continue;
        }

        int stoploss = modifyReq.GetStopLoss();
        int takeprofit = modifyReq.GetTakeProfit();

        if(stoploss < 0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 第 ", i+1, " 個請求止損無效: ", stoploss);
            invalidCount++;
            continue;
        }

        if(takeprofit < 0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 第 ", i+1, " 個請求止盈無效: ", takeprofit);
            invalidCount++;
            continue;
        }

        // 檢查是否至少有一個參數需要修改
        if(stoploss == 0 && takeprofit == 0)
        {
            Print("警告 [OrderExecutor::ValidateBatchModifyRequest]: 第 ", i+1, " 個請求沒有修改內容");
        }

        // 記錄有效的票號
        tickets[validCount] = ticket;
        validCount++;
    }

    Print("資訊 [OrderExecutor::ValidateBatchModifyRequest]: 驗證完成，有效 ", validCount, " 個，無效 ", invalidCount, " 個，重複 ", duplicateCount, " 個");

    // 如果所有請求都無效，返回失敗
    if(validCount == 0)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchModifyRequest]: 所有請求都無效，批次操作取消");
        return false;
    }

    // 如果有部分無效請求，發出警告但允許繼續
    if(invalidCount > 0)
    {
        Print("警告 [OrderExecutor::ValidateBatchModifyRequest]: 有 ", invalidCount, " 個無效請求將被跳過");
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證 BatchCloseRequest 請求                                      |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateBatchCloseRequest(BatchCloseRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: BatchCloseRequest 指針為空");
        return false;
    }

    int count = request.GetRequestCount();
    if(count <= 0)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: 批次請求為空，沒有關閉需要處理");
        return false;
    }

    // 檢查批次大小限制
    const int MAX_BATCH_SIZE = 100;
    if(count > MAX_BATCH_SIZE)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: 批次大小 ", count, " 超過最大限制 ", MAX_BATCH_SIZE);
        return false;
    }

    Print("資訊 [OrderExecutor::ValidateBatchCloseRequest]: 開始驗證批次關閉請求，共 ", count, " 個請求");

    int validCount = 0;
    int invalidCount = 0;
    int duplicateCount = 0;
    int partialCloseCount = 0;
    int fullCloseCount = 0;

    // 用於檢查重複票號的陣列
    int tickets[];
    ArrayResize(tickets, count);

    // 逐一驗證每個請求
    for(int i = 0; i < count; i++)
    {
        CloseRequest* closeReq = request.GetRequest(i);

        if(closeReq == NULL)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: 第 ", i+1, " 個 CloseRequest 為空");
            invalidCount++;
            continue;
        }

        // 基本參數驗證
        int ticket = closeReq.GetTicket();
        if(ticket <= 0)
        {
            Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: 第 ", i+1, " 個請求票號無效: ", ticket);
            invalidCount++;
            continue;
        }

        // 檢查重複票號
        bool isDuplicate = false;
        for(int j = 0; j < validCount; j++)
        {
            if(tickets[j] == ticket)
            {
                Print("警告 [OrderExecutor::ValidateBatchCloseRequest]: 第 ", i+1, " 個請求票號 ", ticket, " 重複");
                isDuplicate = true;
                duplicateCount++;
                break;
            }
        }

        if(isDuplicate)
        {
            invalidCount++;
            continue;
        }

        // 驗證部分關閉的手數
        if(closeReq.IsPartialClose())
        {
            double lots = closeReq.GetLots();
            if(lots <= 0.0)
            {
                Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: 第 ", i+1, " 個請求部分關閉手數無效: ", lots);
                invalidCount++;
                continue;
            }
            partialCloseCount++;
        }
        else
        {
            fullCloseCount++;
        }

        // 記錄有效的票號
        tickets[validCount] = ticket;
        validCount++;
    }

    Print("資訊 [OrderExecutor::ValidateBatchCloseRequest]: 驗證完成");
    Print("  有效請求: ", validCount, " 個");
    Print("  無效請求: ", invalidCount, " 個");
    Print("  重複請求: ", duplicateCount, " 個");
    Print("  完全關閉: ", fullCloseCount, " 個");
    Print("  部分關閉: ", partialCloseCount, " 個");

    // 如果所有請求都無效，返回失敗
    if(validCount == 0)
    {
        Print("錯誤 [OrderExecutor::ValidateBatchCloseRequest]: 所有請求都無效，批次操作取消");
        return false;
    }

    // 如果有部分無效請求，發出警告但允許繼續
    if(invalidCount > 0)
    {
        Print("警告 [OrderExecutor::ValidateBatchCloseRequest]: 有 ", invalidCount, " 個無效請求將被跳過");
    }

    // 特別警告：部分關閉操作的風險
    if(partialCloseCount > 0)
    {
        Print("警告 [OrderExecutor::ValidateBatchCloseRequest]: 包含 ", partialCloseCount, " 個部分關閉操作，請確認手數設定正確");
    }

    return true;
}